.feat2-container {
  padding: 2rem;
  color: #fff;
  text-align: center;
}

.songs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.song-card {
  background-color: #282828;
  border-radius: 8px;
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 250px; /* Fixed height for uniformity */
}

.song-thumbnail {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.no-thumbnail {
  width: 100%;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #444;
  border-radius: 4px;
  font-size: 14px;
  color: #fff;
}

.song-name {
  margin-top: 0.5rem;
  font-size: 1rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.checkbox-label {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pagination {
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.pagination button {
  padding: 0.5rem 1rem;
  background-color: #1db954;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
}

.pagination button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.playlist-form {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #121212;
  border-radius: 8px;
  max-width: 500px;
  margin: 2rem auto;
}

.form-group {
  margin-bottom: 1rem;
  text-align: left;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Add spacing between the checkbox and the label text */
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border-radius: 4px;
  border: none;
}

.form-group input[type="checkbox"] {
  margin: 0; /* Remove default margin for better alignment */
}

.create-btn {
  padding: 0.75rem 1.5rem;
  background-color: #1db954;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1rem;
  color: #fff;
}

.creation-msg {
  margin-top: 1rem;
  font-size: 1rem;
}