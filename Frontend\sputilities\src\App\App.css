.cont-app {
  padding-top: 2rem;
  color: #fff;
  height: 20rem;
}
.head-steps {
  color: #fff;
  font-size: 2.5rem;
  text-align: center;
}
.steps {
  display: flex;
  flex-wrap: wrap;
  text-align: center;
  padding: 10px;
  column-count: 3;
  column-gap: 20px;
}
.step-head {
  color: #1db954;
  font-size: 1.5rem;
}
.step-dis {
  flex: 1 1 300px;
}
.step-p {
  margin: 10px;
  padding: 10px;
  color: #fff;
}
.ban-cont {
  background-color: #191414;
  color: #ffffff;
  padding: 10px;
  text-align: center;
  margin-left: 20rem;
  margin-top: 1.5rem;
  margin-bottom: 5rem;
  margin-right: 20rem;
  height: 10rem;
  border: solid rgb(59, 55, 55);
  border-radius: 10px;
}
.ban-msg {
  margin-top: 20px;
  color: #fff;
  font-size: 20px;
  text-align: center;
}
.ban-star {
  margin-top: 5px;
  height: 1.9rem;
}
.faded {
  background-image: linear-gradient(to bottom, #28b95b, #191414);
}
.why-body {
  height: auto;
  display: flex;
  justify-content: center;
}
.why-flex {
  display: flex;
  text-align: center;
  margin-left: 5rem;
  margin-right: 5rem;
  margin-bottom: 5rem;
}
.why-img {
  height: 30rem;
}
.why-box {
  margin-top: 4.5rem;
  padding-left: 2rem;
}
.why-title {
  color: #ffffff;
  text-align: start;
}
.why-dis {
  color: #ffffff;
  font-size: 1.3rem;
  text-align: start;
  padding-left: 5px;
}

@media screen and (max-width: 800px) {
  .fet-title {
    font-size: 2.2rem;
  }
  .ban-cont {
    margin: auto;
    padding-bottom: 11rem;
  }
  .cont-app {
    height: auto;
    margin-bottom: 2rem;
  }
  .why-flex {
    display: inline;
    margin: auto;
    flex: wrap;
  }
  .why-box {
    margin-top: auto;
  }
  .why-img {
    display: none;
  }
  .why-dis {
    font-size: 20px;
  }
}

/* Animation effects */
.step-dis {
  opacity: 0;
  filter: blur(5px);
  transform: translateX(-100%);
  transition: all 1s;
}
.step-dis.visible {
  opacity: 1;
  filter: blur(0);
  transform: translateX(0);
}
.ban-cont {
  opacity: 0;
  filter: blur(5px);
  transform: translateY(80px);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}
.ban-cont.visible {
  opacity: 1;
  filter: blur(0);
  transform: translateY(0);
}
.feat-box:nth-child(2) {
  transition-delay: 100ms;
}

.feat-box:nth-child(3) {
  transition-delay: 200ms;
}

.feat-box:nth-child(4) {
  transition-delay: 300ms;
}

.feat-box:nth-child(5) {
  transition-delay: 400ms;
}
.feat-box {
  opacity: 0;
  filter: blur(5px);
  transform: translateX(-100%);
  transition: all 1s;
}
.feat-box.visible {
  opacity: 1;
  filter: blur(0);
  transform: translateX(0);
}
.andMore {
  opacity: 0;
  filter: blur(5px);
  transform: translateX(-100%);
  transition: all 1s;
}
.andMore.visible {
  opacity: 1;
  filter: blur(0);
  transform: translateX(0);
}
.grid-item {
  opacity: 0;
  filter: blur(5px);
  transform: translateX(50px);
  transition: all 1s;
}
.grid-item.visible {
  opacity: 1;
  filter: blur(0);
  transform: translateX(0);
}
.grid-item:nth-child(2) {
  transition-delay: 100ms;
}
.grid-item:nth-child(3) {
  transition-delay: 200ms;
}
.grid-item:nth-child(4) {
  transition-delay: 300ms;
}
