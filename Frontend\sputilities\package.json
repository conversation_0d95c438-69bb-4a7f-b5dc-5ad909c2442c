{"name": "sputilities", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.17.9", "@tanstack/react-query-devtools": "^5.80.10", "@tanstack/react-table": "^8.11.6", "@tanstack/react-virtual": "^3.0.1", "axios": "^1.6.5", "clsx": "^2.1.0", "framer-motion": "^10.18.0", "lucide-react": "^0.312.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.49.2", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "react-loader-spinner": "^6.1.6", "react-parallax": "^3.5.1", "react-router-dom": "^6.21.1", "react-spring": "^9.7.3", "react-use": "^17.4.2", "recharts": "^2.10.3", "tailwind-merge": "^2.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.8", "@babel/preset-react": "^7.23.3", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "babel-loader": "^9.1.3", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "^5.0.11", "vitest": "^1.2.0"}}