# Sputilities API Endpoint Configuration

## Base URLs
- **Development**: `http://localhost:3601/api/v1`
- **Frontend**: `http://localhost:5173`

## Environment Variables

### Backend (.env)
```env
# Server Configuration
PORT=3601
NODE_ENV=development

# Spotify API Configuration
SPOTIFY_CLIENT_ID=478250b0aa4a4e6ca9ed489145cebd6a
SPOTIFY_CLIENT_SECRET=b66f699c2af041cdaa6c67e1315882cb
SPOTIFY_REDIRECT_URI=http://localhost:3601/api/v1/auth/callback

# Frontend Configuration
FRONTEND_URL=http://localhost:5173

# Session Configuration
SESSION_SECRET=p1u0n5e6e1t9h1y0

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/sputilities
```

### Frontend (.env)
```env
# API Configuration
VITE_API_URL=http://localhost:3601/api/v1

# Performance Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true

# App Configuration
VITE_APP_NAME=Sputilities
VITE_APP_VERSION=1.0.0
```

## API Endpoints Structure

### Authentication Routes (`/auth`)
- `GET /auth/login` - Initiate Spotify OAuth login
- `GET /auth/callback` - Handle Spotify OAuth callback
- `GET /auth/check` - Check if user is authenticated
- `GET /auth/session` - Get session status and user info
- `GET /auth/token` - Get current access token (debugging)
- `POST /auth/logout` - Logout user and clear session

### Playlist Routes (`/playlists`)
- `GET /playlists` - Get user's playlists with pagination
- `POST /playlists/sync` - Sync playlists from Spotify
- `POST /playlists` - Create a new playlist
- `DELETE /playlists/:id` - Delete a specific playlist
- `POST /playlists/bulk-delete` - Delete multiple playlists
- `GET /playlists/analytics` - Get playlist analytics
- `POST /playlists/:id/tracks` - Add tracks to playlist
- `POST /playlists/:id/albums` - Add album to playlist
- `DELETE /playlists/:id/tracks` - Remove tracks from playlist
- `PUT /playlists/:id/tracks/reorder` - Reorder tracks
- `GET /playlists/:id/tracks/filter` - Filter tracks by audio features
- `GET /playlists/:id/analytics` - Get detailed playlist analytics

### Liked Songs Routes (`/liked-songs`)
- `GET /liked-songs` - Get user's liked songs
- `POST /liked-songs/sync` - Sync liked songs from Spotify
- `POST /liked-songs/like-playlist/:id` - Like all songs from playlist
- `POST /liked-songs/reset` - Reset liked library
- `POST /liked-songs/backup` - Create backup playlist
- `GET /liked-songs/analytics` - Get liked songs analytics

### Operations Routes (`/operations`)
- `GET /operations` - Get user's operations
- `GET /operations/:id` - Get specific operation status
- `POST /operations/:id/cancel` - Cancel operation
- `DELETE /operations/:id` - Delete operation record
- `GET /operations/stats` - Get operation statistics
- `POST /operations/cleanup` - Cleanup old operations

### Playlist Manager Routes (`/playlist-manager`)
- `GET /playlist-manager/dashboard` - Get dashboard data
- `GET /playlist-manager/playlists` - Get playlists with advanced filtering
- `POST /playlist-manager/deletion-preview` - Preview playlist deletion
- `POST /playlist-manager/selective-delete` - Selectively delete playlists
- `GET /playlist-manager/duplicates` - Find duplicate playlists
- `GET /playlist-manager/cleanup-suggestions` - Get cleanup suggestions
- `POST /playlist-manager/batch-operations` - Batch playlist operations

### Track Routes (`/tracks`)
- `GET /tracks/search` - Search for tracks, artists, albums
- `GET /tracks/:id/analytics` - Get track analytics
- `POST /tracks/batch-operations` - Batch track operations

### Smart Management Routes (`/smart`)
- `GET /smart/playlists/:id/duplicates` - Find duplicate tracks
- `POST /smart/playlists/:id/remove-duplicates` - Remove duplicates
- `POST /smart/playlists/compare` - Compare playlists
- `POST /smart/playlists/merge` - Smart merge playlists
- `POST /smart/playlists/merge/preview` - Preview merge
- `PUT /smart/playlists/:id/sort` - Sort playlist tracks
- `GET /smart/genres` - Get available genres

### User Routes (`/users`)
- `GET /users/profile` - Get user profile
- `PUT /users/preferences` - Update user preferences
- `GET /users/statistics` - Get user statistics
- `GET /users/info` - Get basic user info (legacy)
- `DELETE /users/account` - Delete user account

### Legacy Routes (`/legacy`)
All legacy routes are prefixed with `/legacy/` for backward compatibility:
- `GET /legacy/check-login` - Legacy auth check
- `GET /legacy/getUser` - Legacy get user info
- `GET /legacy/session` - Legacy session info
- `GET /legacy/liked` - Legacy fetch liked songs
- `POST /legacy/playlist` - Legacy create playlist
- `GET /legacy/feat_1` - Legacy feature 1
- `GET /legacy/feat_2/liked` - Legacy feature 2 liked
- `POST /legacy/feat_2` - Legacy feature 2

## Frontend API Service Structure

The frontend API service is organized into modules:
- `authAPI` - Authentication operations
- `playlistsAPI` - Playlist management
- `likedSongsAPI` - Liked songs operations
- `operationsAPI` - Operation tracking
- `playlistManagerAPI` - Advanced playlist management
- `tracksAPI` - Track operations
- `smartAPI` - Smart management features
- `usersAPI` - User profile and settings
- `legacyAPI` - Legacy endpoint compatibility

## Status Codes
- `200` - Success
- `201` - Created
- `202` - Accepted (async operation started)
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Rate Limited
- `500` - Internal Server Error

## Response Format
All API responses follow this format:
```json
{
  "success": true|false,
  "data": any,
  "message": "string",
  "timestamp": "ISO 8601 string",
  "error_code": "string (only for errors)",
  "details": "any (only for errors)"
}
```
