.timer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 5%;
  margin-bottom: 0;
  transition: background-color 0.5s ease;
}
.success_body {
  color: #fff;
  margin: 0;
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
}
.feat1-logo {
  margin-top: 3rem;
  height: 5rem;
}
.feat1-link {
  color: #1db954;
  font-size: smaller;
}
.feat1-span {
  color: #ea3621;
  font-size: smaller;
}
.h2-playlist{
  text-align: center;
}