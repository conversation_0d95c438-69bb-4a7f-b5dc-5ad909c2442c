# Sputilities Setup and Testing Guide

## Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (running on localhost:27017)
- Spotify Developer Account

### 1. Backend Setup

```bash
cd Backend/sputilities

# Install dependencies
npm install

# Install dotenv if not already installed
npm install dotenv

# Start the backend server
npm start
```

The backend will start on `http://localhost:3600`

### 2. Frontend Setup

```bash
cd Frontend/sputilities

# Install dependencies
npm install

# Start the development server
npm run dev
```

The frontend will start on `http://localhost:5173`

### 3. Environment Configuration

Ensure both `.env` files are properly configured:

**Backend (.env)**:
```env
PORT=3600
NODE_ENV=development
SPOTIFY_CLIENT_ID=478250b0aa4a4e6ca9ed489145cebd6a
SPOTIFY_CLIENT_SECRET=b66f699c2af041cdaa6c67e1315882cb
SPOTIFY_REDIRECT_URI=http://localhost:3600/api/v1/auth/callback
FRONTEND_URL=http://localhost:5173
SESSION_SECRET=p1u0n5e6e1t9h1y0
MONGODB_URI=mongodb://localhost:27017/sputilities
```

**Frontend (.env)**:
```env
VITE_API_URL=http://localhost:3600/api/v1
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_APP_NAME=Sputilities
VITE_APP_VERSION=1.0.0
```

## Testing the API Endpoints

### Method 1: Automated Testing Script

```bash
# From the root directory
node test-endpoints.js
```

This script will test all endpoints and provide a comprehensive report.

### Method 2: Manual Testing with curl

#### Health Check
```bash
curl http://localhost:3600/api/v1/health
```

#### Authentication Flow
```bash
# 1. Get login URL
curl http://localhost:3600/api/v1/auth/login

# 2. Check auth status (should return false initially)
curl http://localhost:3600/api/v1/auth/check

# 3. After logging in through browser, check again
curl -b cookies.txt http://localhost:3600/api/v1/auth/check
```

#### Test Protected Endpoints (requires authentication)
```bash
# Get playlists
curl -b cookies.txt http://localhost:3600/api/v1/playlists

# Get liked songs
curl -b cookies.txt http://localhost:3600/api/v1/liked-songs

# Get user profile
curl -b cookies.txt http://localhost:3600/api/v1/users/profile
```

### Method 3: Browser Testing

1. Open `http://localhost:5173` in your browser
2. Click "Get Started" to initiate login
3. Complete Spotify OAuth flow
4. Navigate through the application to test different features

## API Endpoint Structure

### New API Routes (Recommended)
- **Base URL**: `http://localhost:3600/api/v1`
- **Authentication**: `/auth/*`
- **Playlists**: `/playlists/*`
- **Liked Songs**: `/liked-songs/*`
- **Operations**: `/operations/*`
- **Playlist Manager**: `/playlist-manager/*`
- **Tracks**: `/tracks/*`
- **Smart Features**: `/smart/*`
- **Users**: `/users/*`

### Legacy API Routes (Backward Compatibility)
- **Base URL**: `http://localhost:3600/api/v1/legacy`
- All legacy endpoints are prefixed with `/legacy/`

## Common Issues and Solutions

### 1. CORS Errors
- Ensure `FRONTEND_URL` in backend .env matches frontend URL
- Check that both servers are running on correct ports

### 2. Authentication Issues
- Verify Spotify client credentials in backend .env
- Check that redirect URI matches in Spotify app settings
- Ensure MongoDB is running and accessible

### 3. Environment Variable Issues
- Restart servers after changing .env files
- Verify dotenv is installed in backend
- Check that VITE_ prefix is used for frontend variables

### 4. Database Connection Issues
- Ensure MongoDB is running: `mongod`
- Check connection string in backend .env
- Verify database permissions

### 5. Port Conflicts
- Backend default: 3600
- Frontend default: 5173
- Change ports in .env files if needed

## Development Workflow

### 1. Making Changes
1. Make code changes
2. Restart appropriate server (backend/frontend)
3. Test endpoints using provided script
4. Verify in browser

### 2. Adding New Endpoints
1. Add route in `Backend/sputilities/src/routes/`
2. Add controller in `Backend/sputilities/src/controllers/`
3. Add API call in `Frontend/sputilities/src/services/api.js`
4. Update documentation

### 3. Debugging
- Check browser console for frontend errors
- Check backend logs for API errors
- Use network tab to inspect API calls
- Run endpoint testing script for quick verification

## Production Deployment

### Environment Variables for Production
```env
NODE_ENV=production
PORT=3600
SPOTIFY_CLIENT_ID=your_production_client_id
SPOTIFY_CLIENT_SECRET=your_production_client_secret
SPOTIFY_REDIRECT_URI=https://yourdomain.com/api/v1/auth/callback
FRONTEND_URL=https://yourdomain.com
SESSION_SECRET=your_secure_session_secret
MONGODB_URI=your_production_mongodb_uri
```

### Build Commands
```bash
# Frontend build
cd Frontend/sputilities
npm run build

# Backend (no build needed, but ensure production dependencies)
cd Backend/sputilities
npm install --production
```

## API Documentation

For detailed API documentation, see:
- `API.md` - Complete API reference
- `ENDPOINT_CONFIGURATION.md` - Endpoint structure and configuration
- Individual route files in `Backend/sputilities/src/routes/`

## Support

If you encounter issues:
1. Check this guide for common solutions
2. Run the endpoint testing script
3. Check server logs for detailed error messages
4. Verify environment configuration
